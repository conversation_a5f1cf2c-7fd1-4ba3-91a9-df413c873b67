"""
Comprehensive QS WUR 2026 Methodology PowerPoint with ALL Lenses, Indicators, and Metrics
Author: Dr. <PERSON><PERSON>, Symbiosis International (Deemed University)
"""

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor
from pptx.enum.shapes import MSO_SHAPE

def create_comprehensive_methodology_presentation():
    """Create comprehensive methodology presentation with all QS WUR components."""
    
    # Load existing presentation
    prs = Presentation('QS_WUR_2026_Analysis_SIU_Final.pptx')
    
    # Define SIU brand colors
    siu_blue = RGBColor(30, 64, 175)
    siu_light_blue = RGBColor(59, 130, 246)
    siu_green = RGBColor(16, 185, 129)
    siu_orange = RGBColor(245, 158, 11)
    siu_red = RGBColor(239, 68, 68)
    gray_text = RGBColor(107, 114, 128)
    
    # Remove the old methodology slide (slide 3) and replace with comprehensive version
    # We'll insert new slides after slide 2
    
    # Slide 3: Complete QS WUR 2026 Framework Overview
    slide3_layout = prs.slide_layouts[1]
    slide3 = prs.slides.add_slide(slide3_layout)
    
    title3 = slide3.shapes.title
    title3.text = "Complete QS WUR 2026 Framework: Lenses, Indicators & Metrics"
    title3.text_frame.paragraphs[0].font.size = Pt(28)
    title3.text_frame.paragraphs[0].font.color.rgb = siu_blue
    title3.text_frame.paragraphs[0].font.bold = True
    
    # Create comprehensive framework content
    framework_box = slide3.shapes.add_textbox(Inches(0.3), Inches(1.5), Inches(12.7), Inches(5.8))
    framework_frame = framework_box.text_frame
    framework_frame.word_wrap = True
    
    framework_text = """🔍 RESEARCH AND DISCOVERY LENS (50% Total Weight)
├── Academic Reputation (30%) - Global survey of academics nominating excellent institutions
├── Citations per Faculty (20%) - Research impact normalized by faculty size (Scopus data)

👥 EMPLOYABILITY AND OUTCOMES LENS (20% Total Weight)  
├── Employer Reputation (15%) - Global survey of employers identifying top graduate producers
├── Employment Outcomes (5%) - Alumni Impact Index × ln(Graduate Employment Rate)

🎓 LEARNING EXPERIENCE LENS (10% Total Weight)
├── Faculty Student Ratio (10%) - Academic staff to student ratio indicating teaching resources

🌍 GLOBAL ENGAGEMENT LENS (15% Total Weight)
├── International Faculty Ratio (5%) - Proportion of international academic staff
├── International Student Ratio (5%) - Proportion of international students  
├── International Research Network (5%) - Sustained global research partnerships (3+ joint papers/5 years)

🌱 SUSTAINABILITY LENS (5% Total Weight)
├── Sustainability (5%) - Alumni climate impact + UN SDG-aligned research

📊 NORMALIZATION METHODOLOGY
• Z-score standardization with locked means/standard deviations for top X institutions
• Min-max scaling (1-100) applied to standardized scores
• Weighted combination based on indicator percentages
• Final institutional score undergoes min-max normalization"""
    
    framework_frame.text = framework_text
    
    # Format the framework content
    for i, paragraph in enumerate(framework_frame.paragraphs):
        if 'LENS' in paragraph.text and 'Total Weight' in paragraph.text:
            paragraph.font.size = Pt(16)
            paragraph.font.bold = True
            paragraph.font.color.rgb = siu_blue
            paragraph.space_after = Pt(8)
        elif paragraph.text.startswith('├──'):
            paragraph.font.size = Pt(14)
            paragraph.font.color.rgb = RGBColor(51, 51, 51)
            paragraph.level = 1
            paragraph.space_after = Pt(4)
        elif 'NORMALIZATION METHODOLOGY' in paragraph.text:
            paragraph.font.size = Pt(16)
            paragraph.font.bold = True
            paragraph.font.color.rgb = siu_orange
            paragraph.space_before = Pt(12)
            paragraph.space_after = Pt(8)
        elif paragraph.text.startswith('•'):
            paragraph.font.size = Pt(13)
            paragraph.font.color.rgb = RGBColor(51, 51, 51)
            paragraph.level = 1
            paragraph.space_after = Pt(3)
    
    # Slide 4: Detailed Methodology Changes Table
    slide4_layout = prs.slide_layouts[1]
    slide4 = prs.slides.add_slide(slide4_layout)
    
    title4 = slide4.shapes.title
    title4.text = "QS WUR 2026: Complete Methodology Changes Analysis"
    title4.text_frame.paragraphs[0].font.size = Pt(30)
    title4.text_frame.paragraphs[0].font.color.rgb = siu_blue
    title4.text_frame.paragraphs[0].font.bold = True
    
    # Create comprehensive table for all indicators
    rows, cols = 10, 6
    table_left = Inches(0.2)
    table_top = Inches(1.6)
    table_width = Inches(12.9)
    table_height = Inches(5.2)
    
    table = slide4.shapes.add_table(rows, cols, table_left, table_top, table_width, table_height).table
    
    # Set column widths
    table.columns[0].width = Inches(2.8)  # Lens
    table.columns[1].width = Inches(2.5)  # Indicator
    table.columns[2].width = Inches(1.3)  # 2024 Weight
    table.columns[3].width = Inches(1.3)  # 2025/26 Weight
    table.columns[4].width = Inches(1.0)  # Change
    table.columns[5].width = Inches(4.0)  # Data Source/Description
    
    # Header row
    headers = ['Lens', 'Indicator', '2024\nWeight', '2025/26\nWeight', 'Change', 'Data Source & Description']
    for i, header in enumerate(headers):
        cell = table.cell(0, i)
        cell.text = header
        cell.text_frame.paragraphs[0].font.bold = True
        cell.text_frame.paragraphs[0].font.size = Pt(12)
        cell.text_frame.paragraphs[0].font.color.rgb = RGBColor(255, 255, 255)
        cell.text_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
        cell.fill.solid()
        cell.fill.fore_color.rgb = siu_blue
    
    # Complete data for all indicators
    data = [
        ['Research &\nDiscovery', 'Academic Reputation', '40%', '30%', '-10%', 'Global academic survey (research excellence)'],
        ['Research &\nDiscovery', 'Citations per Faculty', '20%', '20%', '0%', 'Scopus citation data normalized by faculty'],
        ['Employability &\nOutcomes', 'Employer Reputation', '10%', '15%', '+5%', 'Global employer survey (graduate quality)'],
        ['Employability &\nOutcomes', 'Employment Outcomes', '0%', '5%', '+5%', 'NEW: Alumni Impact × Employment Rate'],
        ['Learning\nExperience', 'Faculty Student Ratio', '20%', '10%', '-10%', 'Academic staff to student ratio'],
        ['Global\nEngagement', 'International Faculty', '5%', '5%', '0%', 'Proportion of international staff'],
        ['Global\nEngagement', 'International Students', '5%', '5%', '0%', 'Proportion of international students'],
        ['Global\nEngagement', 'Intl Research Network', '0%', '5%', '+5%', 'NEW: Sustained global partnerships'],
        ['Sustainability', 'Sustainability', '0%', '5%', '+5%', 'NEW: Climate impact + UN SDG research']
    ]
    
    for i, row_data in enumerate(data, 1):
        for j, cell_data in enumerate(row_data):
            cell = table.cell(i, j)
            cell.text = cell_data
            
            # Font sizing based on column
            if j in [0, 1, 5]:  # Lens, Indicator, Description columns
                cell.text_frame.paragraphs[0].font.size = Pt(10)
            else:  # Weight and change columns
                cell.text_frame.paragraphs[0].font.size = Pt(11)
                cell.text_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
            
            # Color coding for changes
            if j == 4:  # Change column
                if cell_data.startswith('+'):
                    cell.text_frame.paragraphs[0].font.color.rgb = siu_green
                    cell.text_frame.paragraphs[0].font.bold = True
                elif cell_data.startswith('-'):
                    cell.text_frame.paragraphs[0].font.color.rgb = siu_red
                    cell.text_frame.paragraphs[0].font.bold = True
                else:
                    cell.text_frame.paragraphs[0].font.color.rgb = gray_text
            
            # Highlight new indicators
            if 'NEW:' in cell_data:
                cell.text_frame.paragraphs[0].font.color.rgb = siu_orange
                cell.text_frame.paragraphs[0].font.bold = True
    
    # Add key insights box
    insights_box = slide4.shapes.add_textbox(Inches(0.5), Inches(6.9), Inches(12), Inches(0.5))
    insights_frame = insights_box.text_frame
    insights_frame.text = "🔑 Key Insight: 50% shift from input metrics (Academic Rep + Faculty Ratio: 60%→40%) to outcome metrics (Employment + Networks + Sustainability: 0%→15%)"
    insights_frame.paragraphs[0].font.size = Pt(14)
    insights_frame.paragraphs[0].font.bold = True
    insights_frame.paragraphs[0].font.color.rgb = siu_orange
    insights_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
    
    # Save the updated presentation
    prs.save('QS_WUR_2026_Analysis_SIU_Complete_Methodology.pptx')
    
    return prs

def create_methodology_visualization():
    """Create comprehensive methodology visualization showing all components."""
    
    import matplotlib.pyplot as plt
    import matplotlib.patches as patches
    import numpy as np
    
    # Set up the figure
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 16))
    fig.suptitle('QS World University Rankings 2026: Complete Methodology Framework\nDr. Dharmendra Pandey | Symbiosis International University', 
                 fontsize=20, fontweight='bold', y=0.95)
    
    # Colors
    colors = {
        'research': '#1e40af',
        'employability': '#059669', 
        'learning': '#d97706',
        'global': '#7c3aed',
        'sustainability': '#dc2626'
    }
    
    # 1. Complete Weight Distribution (Pie Chart)
    lenses = ['Research &\nDiscovery', 'Employability &\nOutcomes', 'Learning\nExperience', 
              'Global\nEngagement', 'Sustainability']
    weights = [50, 20, 10, 15, 5]
    lens_colors = [colors['research'], colors['employability'], colors['learning'], 
                   colors['global'], colors['sustainability']]
    
    wedges, texts, autotexts = ax1.pie(weights, labels=lenses, autopct='%1.0f%%',
                                      colors=lens_colors, startangle=90, textprops={'fontsize': 12})
    ax1.set_title('QS WUR 2026: Complete Lens Distribution', fontsize=16, fontweight='bold', pad=20)
    
    for autotext in autotexts:
        autotext.set_color('white')
        autotext.set_fontweight('bold')
        autotext.set_fontsize(14)
    
    # 2. All Indicators with Weights
    indicators = ['Academic\nReputation', 'Citations per\nFaculty', 'Employer\nReputation', 
                 'Employment\nOutcomes', 'Faculty Student\nRatio', 'International\nFaculty', 
                 'International\nStudents', 'International\nResearch Network', 'Sustainability']
    
    weights_2024 = [40, 20, 10, 0, 20, 5, 5, 0, 0]
    weights_2026 = [30, 20, 15, 5, 10, 5, 5, 5, 5]
    changes = [w2026 - w2024 for w2024, w2026 in zip(weights_2024, weights_2026)]
    
    x = np.arange(len(indicators))
    width = 0.35
    
    bars1 = ax2.bar(x - width/2, weights_2024, width, label='2024 Weights', 
                   color='lightblue', alpha=0.7, edgecolor='navy')
    bars2 = ax2.bar(x + width/2, weights_2026, width, label='2025/26 Weights', 
                   color='darkblue', alpha=0.8, edgecolor='navy')
    
    # Add change indicators
    for i, change in enumerate(changes):
        if change > 0:
            ax2.annotate(f'+{change}%', xy=(i, max(weights_2024[i], weights_2026[i]) + 1),
                        ha='center', va='bottom', fontweight='bold', color='green', fontsize=10)
        elif change < 0:
            ax2.annotate(f'{change}%', xy=(i, max(weights_2024[i], weights_2026[i]) + 1),
                        ha='center', va='bottom', fontweight='bold', color='red', fontsize=10)
    
    ax2.set_xlabel('All QS WUR Indicators', fontsize=14, fontweight='bold')
    ax2.set_ylabel('Weight Percentage (%)', fontsize=14, fontweight='bold')
    ax2.set_title('Complete Indicator Weight Changes: 2024 vs 2025/26', fontsize=16, fontweight='bold')
    ax2.set_xticks(x)
    ax2.set_xticklabels(indicators, rotation=45, ha='right', fontsize=10)
    ax2.legend(fontsize=12)
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(0, 45)
    
    # 3. Lens-wise breakdown
    lens_data = {
        'Research & Discovery': {'indicators': ['Academic Reputation', 'Citations per Faculty'], 
                                'weights': [30, 20], 'total': 50},
        'Employability & Outcomes': {'indicators': ['Employer Reputation', 'Employment Outcomes'], 
                                   'weights': [15, 5], 'total': 20},
        'Learning Experience': {'indicators': ['Faculty Student Ratio'], 
                              'weights': [10], 'total': 10},
        'Global Engagement': {'indicators': ['Intl Faculty', 'Intl Students', 'Intl Research'], 
                            'weights': [5, 5, 5], 'total': 15},
        'Sustainability': {'indicators': ['Sustainability'], 
                         'weights': [5], 'total': 5}
    }
    
    y_pos = 0
    for lens, data in lens_data.items():
        # Draw lens bar
        ax3.barh(y_pos, data['total'], height=0.8, 
                color=lens_colors[list(lens_data.keys()).index(lens)], alpha=0.7)
        ax3.text(data['total']/2, y_pos, f"{lens}\n({data['total']}%)", 
                ha='center', va='center', fontweight='bold', fontsize=10, color='white')
        
        # Add indicator breakdown
        x_offset = 0
        for i, (indicator, weight) in enumerate(zip(data['indicators'], data['weights'])):
            ax3.barh(y_pos - 0.3, weight, left=x_offset, height=0.2, 
                    color=lens_colors[list(lens_data.keys()).index(lens)], alpha=0.5)
            ax3.text(x_offset + weight/2, y_pos - 0.3, f"{weight}%", 
                    ha='center', va='center', fontsize=8, fontweight='bold')
            x_offset += weight
        
        y_pos += 1
    
    ax3.set_xlim(0, 55)
    ax3.set_ylim(-0.5, len(lens_data) - 0.5)
    ax3.set_xlabel('Weight Percentage (%)', fontsize=14, fontweight='bold')
    ax3.set_title('Lens-wise Indicator Breakdown', fontsize=16, fontweight='bold')
    ax3.set_yticks([])
    ax3.grid(True, alpha=0.3, axis='x')
    
    # 4. Methodology Process Flow
    ax4.text(0.5, 0.9, 'QS WUR 2026 Methodology Process', ha='center', va='center', 
            fontsize=16, fontweight='bold', transform=ax4.transAxes)
    
    process_steps = [
        "1. Data Collection\n• Academic/Employer Surveys\n• Scopus Citation Data\n• Institutional Data",
        "2. Raw Data Processing\n• Ratio Calculations\n• Capping & Damping\n• Quality Checks",
        "3. Z-Score Normalization\n• Standardization\n• Locked Means/StdDev\n• Normal Distribution",
        "4. Min-Max Scaling\n• 0-1 Range Conversion\n• 1-100 Final Scaling\n• Weighted Combination",
        "5. Final Ranking\n• Overall Score Calculation\n• Final Min-Max Normalization\n• Rank Assignment"
    ]
    
    for i, step in enumerate(process_steps):
        y_position = 0.75 - (i * 0.15)
        
        # Draw process box
        box = patches.FancyBboxPatch((0.05, y_position - 0.06), 0.9, 0.1,
                                   boxstyle="round,pad=0.01", 
                                   facecolor=lens_colors[i % len(lens_colors)], 
                                   alpha=0.3, edgecolor='black')
        ax4.add_patch(box)
        
        ax4.text(0.5, y_position, step, ha='center', va='center', 
                fontsize=10, fontweight='bold', transform=ax4.transAxes)
        
        # Draw arrow to next step
        if i < len(process_steps) - 1:
            ax4.annotate('', xy=(0.5, y_position - 0.08), xytext=(0.5, y_position - 0.05),
                        arrowprops=dict(arrowstyle='->', lw=2, color='black'),
                        transform=ax4.transAxes)
    
    ax4.set_xlim(0, 1)
    ax4.set_ylim(0, 1)
    ax4.axis('off')
    
    plt.tight_layout()
    plt.savefig('QS_WUR_2026_Complete_Methodology_Framework.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

if __name__ == "__main__":
    print("Creating comprehensive QS WUR 2026 methodology presentation...")
    
    # Create the comprehensive methodology presentation
    prs = create_comprehensive_methodology_presentation()
    
    # Create comprehensive methodology visualization
    create_methodology_visualization()
    
    print("✓ Comprehensive methodology presentation created: QS_WUR_2026_Analysis_SIU_Complete_Methodology.pptx")
    print("✓ Complete methodology framework visualization: QS_WUR_2026_Complete_Methodology_Framework.png")
    print("\nNow includes:")
    print("• All 5 lenses with complete descriptions")
    print("• All 9 indicators with data sources")
    print("• Complete normalization methodology")
    print("• Detailed weight change analysis")
    print("• Comprehensive framework visualization")
    print("\n🎯 Ready for Directors Meeting with complete methodology coverage!")
