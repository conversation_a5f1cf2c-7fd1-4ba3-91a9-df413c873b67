"""
Fix QS WUR 2026 Process Flow Chart - Better Spacing and Clarity
Author: Dr<PERSON>, Symbiosis International (Deemed University)
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np

def create_improved_process_flow():
    """Create an improved, clearer process flow chart with better spacing."""
    
    # Set up styling
    plt.style.use('seaborn-v0_8-whitegrid')
    
    # Colors
    colors = {
        'research': '#1e40af',
        'employability': '#059669', 
        'learning': '#d97706',
        'global': '#7c3aed',
        'sustainability': '#dc2626'
    }
    
    # Create figure with better proportions
    fig, ax = plt.subplots(figsize=(14, 18))
    
    process_steps = [
        {
            "title": "1. DATA COLLECTION",
            "details": [
                "Academic Reputation Survey (60,000+ academics)",
                "Employer Reputation Survey (40,000+ employers)", 
                "Scopus Citation Database Analysis",
                "Institutional Data Submissions",
                "Alumni Employment Tracking"
            ]
        },
        {
            "title": "2. RAW DATA PROCESSING", 
            "details": [
                "Calculate Faculty-Student Ratios",
                "Compute International Staff/Student Percentages",
                "Apply Capping & Damping Mechanisms",
                "Data Quality Validation & Cleaning",
                "Missing Data Imputation"
            ]
        },
        {
            "title": "3. Z-SCORE NORMALIZATION",
            "details": [
                "Standardization: (X - μ) / σ",
                "Locked Means/Standard Deviations",
                "Top X Institution Benchmarking",
                "Normal Distribution Mapping",
                "Outlier Adjustment"
            ]
        },
        {
            "title": "4. MIN-MAX SCALING",
            "details": [
                "Convert Z-scores to 0-1 Range",
                "Apply 1-100 Final Scaling",
                "Weight Individual Indicators",
                "Calculate Lens Scores",
                "Combine Weighted Indicators"
            ]
        },
        {
            "title": "5. FINAL RANKING",
            "details": [
                "Sum All Weighted Lens Scores",
                "Apply Final Min-Max Normalization",
                "Assign Global Rank Positions",
                "Quality Assurance Checks",
                "Publish Final Rankings"
            ]
        }
    ]
    
    step_colors = [colors['research'], colors['employability'], colors['learning'], 
                   colors['global'], colors['sustainability']]
    
    # Calculate spacing
    total_height = 0.85
    step_spacing = total_height / len(process_steps)
    
    for i, step in enumerate(process_steps):
        # Calculate positions
        y_top = 0.9 - (i * step_spacing)
        y_bottom = y_top - (step_spacing * 0.8)  # Leave 20% for spacing
        
        # Main step box
        main_box_height = 0.08
        main_box_y = y_top - main_box_height
        
        main_box = patches.FancyBboxPatch(
            (0.1, main_box_y), 0.8, main_box_height,
            boxstyle="round,pad=0.01", 
            facecolor=step_colors[i], 
            alpha=0.9, 
            edgecolor='black', 
            linewidth=2
        )
        ax.add_patch(main_box)
        
        # Step title
        ax.text(0.5, main_box_y + main_box_height/2, step["title"], 
                ha='center', va='center', 
                fontsize=16, fontweight='bold', color='white', 
                transform=ax.transAxes)
        
        # Detail boxes with proper spacing
        detail_box_height = 0.025
        detail_spacing = 0.005
        details_start_y = main_box_y - 0.02
        
        for j, detail in enumerate(step["details"]):
            detail_y = details_start_y - (j * (detail_box_height + detail_spacing))
            
            # Detail background box
            detail_box = patches.FancyBboxPatch(
                (0.15, detail_y), 0.7, detail_box_height,
                boxstyle="round,pad=0.003", 
                facecolor=step_colors[i], 
                alpha=0.2, 
                edgecolor=step_colors[i],
                linewidth=1
            )
            ax.add_patch(detail_box)
            
            # Detail text
            ax.text(0.5, detail_y + detail_box_height/2, f"• {detail}", 
                    ha='center', va='center', 
                    fontsize=11, fontweight='normal', 
                    color='black',
                    transform=ax.transAxes)
        
        # Arrow to next step (if not last step)
        if i < len(process_steps) - 1:
            arrow_start_y = y_bottom + 0.02
            arrow_end_y = arrow_start_y - 0.04
            
            ax.annotate('', 
                       xy=(0.5, arrow_end_y), 
                       xytext=(0.5, arrow_start_y),
                       arrowprops=dict(
                           arrowstyle='->', 
                           lw=4, 
                           color='darkblue',
                           alpha=0.8
                       ),
                       transform=ax.transAxes)
    
    # Set title and formatting
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.set_title('QS World University Rankings 2026\nComplete Methodology Process Flow', 
                 fontsize=20, fontweight='bold', pad=30)
    ax.axis('off')
    
    # Add subtitle
    fig.text(0.5, 0.02, 'Dr. Dharmendra Pandey | Symbiosis International University', 
             ha='center', fontsize=12, style='italic')
    
    # Add side annotations for clarity
    ax.text(0.02, 0.5, 'DATA\nINPUTS', ha='center', va='center', 
            fontsize=14, fontweight='bold', rotation=90, 
            transform=ax.transAxes, color='darkblue')
    
    ax.text(0.98, 0.5, 'RANKING\nOUTPUTS', ha='center', va='center', 
            fontsize=14, fontweight='bold', rotation=90, 
            transform=ax.transAxes, color='darkblue')
    
    plt.tight_layout()
    plt.savefig('QS_WUR_2026_Process_Flow_Improved.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

def create_horizontal_process_flow():
    """Create an alternative horizontal process flow for better readability."""
    
    # Set up styling
    plt.style.use('seaborn-v0_8-whitegrid')
    
    # Colors
    colors = {
        'research': '#1e40af',
        'employability': '#059669', 
        'learning': '#d97706',
        'global': '#7c3aed',
        'sustainability': '#dc2626'
    }
    
    # Create horizontal figure
    fig, ax = plt.subplots(figsize=(20, 12))
    
    process_steps = [
        {
            "title": "DATA\nCOLLECTION",
            "details": [
                "Academic Survey",
                "Employer Survey", 
                "Scopus Citations",
                "Institutional Data"
            ]
        },
        {
            "title": "DATA\nPROCESSING", 
            "details": [
                "Ratio Calculations",
                "Quality Validation",
                "Capping & Damping",
                "Missing Data Handling"
            ]
        },
        {
            "title": "Z-SCORE\nNORMALIZATION",
            "details": [
                "Standardization",
                "Locked Benchmarks",
                "Distribution Mapping",
                "Outlier Adjustment"
            ]
        },
        {
            "title": "MIN-MAX\nSCALING",
            "details": [
                "0-1 Conversion",
                "1-100 Scaling",
                "Weight Application",
                "Score Combination"
            ]
        },
        {
            "title": "FINAL\nRANKING",
            "details": [
                "Score Summation",
                "Final Normalization",
                "Rank Assignment",
                "Quality Assurance"
            ]
        }
    ]
    
    step_colors = [colors['research'], colors['employability'], colors['learning'], 
                   colors['global'], colors['sustainability']]
    
    # Calculate horizontal spacing
    step_width = 0.16
    step_spacing = 0.02
    
    for i, step in enumerate(process_steps):
        # Calculate x position
        x_left = 0.05 + (i * (step_width + step_spacing))
        
        # Main step box
        main_box = patches.FancyBboxPatch(
            (x_left, 0.6), step_width, 0.15,
            boxstyle="round,pad=0.01", 
            facecolor=step_colors[i], 
            alpha=0.9, 
            edgecolor='black', 
            linewidth=2
        )
        ax.add_patch(main_box)
        
        # Step title
        ax.text(x_left + step_width/2, 0.675, step["title"], 
                ha='center', va='center', 
                fontsize=14, fontweight='bold', color='white', 
                transform=ax.transAxes)
        
        # Detail boxes
        detail_box_height = 0.08
        for j, detail in enumerate(step["details"]):
            detail_y = 0.45 - (j * 0.09)
            
            # Detail box
            detail_box = patches.FancyBboxPatch(
                (x_left + 0.01, detail_y), step_width - 0.02, detail_box_height,
                boxstyle="round,pad=0.005", 
                facecolor=step_colors[i], 
                alpha=0.3, 
                edgecolor=step_colors[i],
                linewidth=1
            )
            ax.add_patch(detail_box)
            
            # Detail text
            ax.text(x_left + step_width/2, detail_y + detail_box_height/2, detail, 
                    ha='center', va='center', 
                    fontsize=10, fontweight='bold', 
                    color='black',
                    transform=ax.transAxes)
        
        # Arrow to next step
        if i < len(process_steps) - 1:
            arrow_x = x_left + step_width + step_spacing/2
            ax.annotate('', 
                       xy=(arrow_x + 0.01, 0.675), 
                       xytext=(arrow_x - 0.01, 0.675),
                       arrowprops=dict(
                           arrowstyle='->', 
                           lw=4, 
                           color='darkblue',
                           alpha=0.8
                       ),
                       transform=ax.transAxes)
    
    # Set title and formatting
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.set_title('QS World University Rankings 2026: Methodology Process Flow\nFrom Raw Data to Final Rankings', 
                 fontsize=18, fontweight='bold', pad=30)
    ax.axis('off')
    
    # Add subtitle
    fig.text(0.5, 0.02, 'Dr. Dharmendra Pandey | Symbiosis International University', 
             ha='center', fontsize=12, style='italic')
    
    plt.tight_layout()
    plt.savefig('QS_WUR_2026_Process_Flow_Horizontal.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

if __name__ == "__main__":
    print("Creating improved QS WUR 2026 process flow charts...")
    
    # Create improved vertical process flow
    create_improved_process_flow()
    print("✓ Improved vertical process flow created: QS_WUR_2026_Process_Flow_Improved.png")
    
    # Create horizontal process flow
    create_horizontal_process_flow()
    print("✓ Horizontal process flow created: QS_WUR_2026_Process_Flow_Horizontal.png")
    
    print("\nBoth versions created:")
    print("• Vertical layout with better spacing and no overlaps")
    print("• Horizontal layout for alternative presentation style")
    print("• Clear text, proper box sizing, and professional appearance")
    print("\n🎯 Process flow charts now ready for clear presentation!")
